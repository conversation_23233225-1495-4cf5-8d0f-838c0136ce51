const mongoose = require('mongoose');

const pushTokenSchema = new mongoose.Schema({
  token: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  platform: {
    type: String,
    required: true,
    enum: ['ios', 'android']
  },
  deviceId: {
    type: String,
    default: 'unknown'
  },
  language: {
    type: String,
    required: true,
    enum: ['en', 'es', 'dom'],
    default: 'en'
  },
  notificationEnabled: {
    type: Boolean,
    default: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastUsed: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for efficient queries
pushTokenSchema.index({ platform: 1, isActive: 1 });
pushTokenSchema.index({ language: 1, isActive: 1 });
pushTokenSchema.index({ platform: 1, language: 1, isActive: 1 });
pushTokenSchema.index({ createdAt: -1 });

// Update lastUsed when token is accessed
pushTokenSchema.pre('findOne', function() {
  this.set({ lastUsed: new Date() });
});

// Method to mark token as inactive (for cleanup)
pushTokenSchema.methods.deactivate = function() {
  this.isActive = false;
  return this.save();
};

// Static method to get active tokens
pushTokenSchema.statics.getActiveTokens = function(platform = null, language = null) {
  const query = { isActive: true, notificationEnabled: true };
  if (platform) {
    query.platform = platform;
  }
  if (language) {
    query.language = language;
  }
  return this.find(query).sort({ createdAt: -1 });
};

// Static method to cleanup old inactive tokens
pushTokenSchema.statics.cleanupOldTokens = function(daysOld = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);

  return this.deleteMany({
    isActive: false,
    updatedAt: { $lt: cutoffDate }
  });
};

module.exports = mongoose.model('PushToken', pushTokenSchema);
