const express = require('express');
const router = express.Router();
const migrationController = require('../controllers/migrationController');
const authMiddleware = require('../middleware/auth');

/**
 * @swagger
 * tags:
 *   name: Migration
 *   description: Migration settings management (Admin only)
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     MigrationSettings:
 *       type: object
 *       properties:
 *         migrationEnabled:
 *           type: boolean
 *           description: Whether migration mode is enabled
 *         migrationMessage:
 *           type: object
 *           properties:
 *             field_en:
 *               type: string
 *               description: Migration message in English
 *             field_es:
 *               type: string
 *               description: Migration message in Spanish
 *             field_dom:
 *               type: string
 *               description: Migration message in Dominican Spanish
 *         migrationEnabledAt:
 *           type: string
 *           format: date-time
 *           description: When migration was last enabled
 *         migrationDisabledAt:
 *           type: string
 *           format: date-time
 *           description: When migration was last disabled
 *         lastModifiedBy:
 *           type: string
 *           description: Who last modified the settings
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

// All migration routes require authentication
router.use(authMiddleware);

/**
 * @swagger
 * /api/admin/migration:
 *   get:
 *     summary: Get current migration settings
 *     tags: [Migration]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Migration settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/MigrationSettings'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', migrationController.getMigrationSettings);

/**
 * @swagger
 * /api/admin/migration/toggle:
 *   post:
 *     summary: Toggle migration on/off
 *     tags: [Migration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - enabled
 *             properties:
 *               enabled:
 *                 type: boolean
 *                 description: Whether to enable or disable migration
 *     responses:
 *       200:
 *         description: Migration toggled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/MigrationSettings'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/toggle', migrationController.toggleMigration);

/**
 * @swagger
 * /api/admin/migration/message:
 *   put:
 *     summary: Update migration message
 *     tags: [Migration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - migrationMessage
 *             properties:
 *               migrationMessage:
 *                 type: object
 *                 required:
 *                   - field_en
 *                   - field_es
 *                   - field_dom
 *                 properties:
 *                   field_en:
 *                     type: string
 *                     description: Migration message in English
 *                   field_es:
 *                     type: string
 *                     description: Migration message in Spanish
 *                   field_dom:
 *                     type: string
 *                     description: Migration message in Dominican Spanish
 *     responses:
 *       200:
 *         description: Migration message updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/MigrationSettings'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put('/message', migrationController.updateMigrationMessage);

/**
 * @swagger
 * /api/admin/migration/status:
 *   get:
 *     summary: Get migration status (lightweight)
 *     tags: [Migration]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Migration status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     migrationEnabled:
 *                       type: boolean
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/status', migrationController.getMigrationStatus);

module.exports = router;
