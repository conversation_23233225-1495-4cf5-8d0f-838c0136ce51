import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState } from 'react-native';
import { initRevenueCat, checkIfUserHasAccess } from '@/services/revenueCatService';
import { PenaltyCategory, PenaltySelection, savePenaltySelections, loadPenaltySelections, saveIndividualPenaltySelections, loadIndividualPenaltySelections, getDefaultPenaltyIds } from '@/services/penaltyService';
import { pushNotificationService } from '@/services/pushNotificationService';

interface AppSettingsContextProps {
  isSoundOn: boolean;
  isHapticsOn: boolean;
  isPremiumUnlocked: boolean;
  selectedPenalties: PenaltyCategory[];
  selectedIndividualPenalties: PenaltySelection[];
  notificationPreference: boolean;
  toggleSound: () => void;
  toggleHaptics: () => void;
  unlockPremium: () => void;
  updatePenaltySelections: (selections: PenaltyCategory[]) => void;
  updateIndividualPenaltySelections: (selections: PenaltySelection[]) => void;
  retrySetDefaultPenalties: () => Promise<void>;
  setNotificationPreference: (enabled: boolean) => Promise<void>;
}

const AppSettingsContext = createContext<AppSettingsContextProps>({
  isSoundOn: true,
  isHapticsOn: true,
  isPremiumUnlocked: false,
  selectedPenalties: [],
  selectedIndividualPenalties: [],
  notificationPreference: false,
  toggleSound: () => {},
  toggleHaptics: () => {},
  unlockPremium: () => {},
  updatePenaltySelections: () => {},
  updateIndividualPenaltySelections: () => {},
  retrySetDefaultPenalties: async () => {},
  setNotificationPreference: async () => {},
});

export const AppSettingsProvider = ({ children }: { children: ReactNode }) => {
  useEffect(() => {
    // configure SDK on app launch
    initRevenueCat();
  }, []);

  const [isSoundOn, setIsSoundOn] = useState(true);
  const [isHapticsOn, setIsHapticsOn] = useState(true);
  const [isPremiumUnlocked, setIsPremiumUnlocked] = useState(false);
  const [selectedPenalties, setSelectedPenalties] = useState<PenaltyCategory[]>([]);
  const [selectedIndividualPenalties, setSelectedIndividualPenalties] = useState<PenaltySelection[]>([]);
  const [notificationPreference, setNotificationPreferenceState] = useState(false);

  useEffect(() => {
      const loadSettings = async () => {
      const sound = await AsyncStorage.getItem('soundEnabled');
      const haptics = await AsyncStorage.getItem('hapticsEnabled');
      if (sound !== null) setIsSoundOn(sound === 'true');
      if (haptics !== null) setIsHapticsOn(haptics === 'true');

      // Load penalty selections (both category and individual)
      const penalties = await loadPenaltySelections();
      setSelectedPenalties(penalties);

      const individualPenalties = await loadIndividualPenaltySelections();

      // Load notification preference
      const notificationPref = await pushNotificationService.getNotificationPreference();
      setNotificationPreferenceState(notificationPref);

      // If no individual penalties are selected, try to set defaults based on premium status
      if (individualPenalties.length === 0) {
        console.log('🎯 No individual penalties selected, checking for defaults...');
        // We'll check premium status and set defaults after premium status is loaded
        setSelectedIndividualPenalties([]);
      } else {
        setSelectedIndividualPenalties(individualPenalties);
      }
      };
      loadSettings();
  }, []);

  useEffect(() => {
    const loadPremiumStatus = async () => {
      const hasAccess = await checkIfUserHasAccess();
      setIsPremiumUnlocked(hasAccess);
      await AsyncStorage.setItem('premiumUnlocked', hasAccess ? 'true' : 'false');

      // After premium status is loaded, check if we need to set default penalties
      await setDefaultPenaltiesIfNeeded(hasAccess);
    };
    loadPremiumStatus();
  }, []);

  // Function to set default penalties if none are selected
  const setDefaultPenaltiesIfNeeded = async (isPremiumUser: boolean) => {
    try {
      const currentSelections = await loadIndividualPenaltySelections();

      if (currentSelections.length === 0) {
        console.log('🎯 Setting default penalties for', isPremiumUser ? 'premium' : 'free', 'user');
        const defaultPenaltyIds = await getDefaultPenaltyIds(isPremiumUser);

        if (defaultPenaltyIds.length > 0) {
          console.log('🎯 Found default penalties:', defaultPenaltyIds);
          setSelectedIndividualPenalties(defaultPenaltyIds);
          await saveIndividualPenaltySelections(defaultPenaltyIds);
        } else {
          console.log('🎯 No default penalties configured for', isPremiumUser ? 'premium' : 'free', 'users');
        }
      }
    } catch (error) {
      console.error('Error setting default penalties:', error);
    }
  };

  // Function to retry setting default penalties (can be called after penalty content is fetched)
  const retrySetDefaultPenalties = async () => {
    try {
      console.log('🎯 Retrying to set default penalties...');
      await setDefaultPenaltiesIfNeeded(isPremiumUnlocked);
    } catch (error) {
      console.error('Error retrying to set default penalties:', error);
    }
  };

  useEffect(() => {
    const subscription = AppState.addEventListener('change', async nextState => {
      if (nextState === 'active') {
        const hasAccess = await checkIfUserHasAccess();
        setIsPremiumUnlocked(hasAccess);
        await AsyncStorage.setItem('premiumUnlocked', hasAccess ? 'true' : 'false');
      }
    });
    return () => subscription.remove();
  }, []);

  const toggleSound = async () => {
      const newValue = !isSoundOn;
      setIsSoundOn(newValue);
      await AsyncStorage.setItem('soundEnabled', String(newValue));
  };

  const toggleHaptics = async () => {
      const newValue = !isHapticsOn;
      setIsHapticsOn(newValue);
      await AsyncStorage.setItem('hapticsEnabled', String(newValue));
  };

  const unlockPremium = async () => {
    setIsPremiumUnlocked(true);
    await AsyncStorage.setItem('premiumUnlocked', 'true');
  };

  const updatePenaltySelections = async (selections: PenaltyCategory[]) => {
    setSelectedPenalties(selections);
    await savePenaltySelections(selections);
  };

  const updateIndividualPenaltySelections = async (selections: PenaltySelection[]) => {
    setSelectedIndividualPenalties(selections);
    await saveIndividualPenaltySelections(selections);
  };

  const setNotificationPreference = async (enabled: boolean) => {
    setNotificationPreferenceState(enabled);
    await pushNotificationService.setNotificationPreference(enabled);

    if (enabled) {
      // When enabling notifications, register the token (which includes the preference)
      // This handles both new devices and existing devices
      await pushNotificationService.handleAutoTokenRegistration();
    } else {
      // When disabling notifications, only update backend if device is registered
      // The 404 error is handled gracefully in updateNotificationPreferenceOnBackend
      await pushNotificationService.updateNotificationPreferenceOnBackend(enabled);
    }
  };

  return (
      <AppSettingsContext.Provider
      value={{
          isSoundOn,
          isHapticsOn,
          isPremiumUnlocked,
          selectedPenalties,
          selectedIndividualPenalties,
          notificationPreference,
          toggleSound,
          toggleHaptics,
          unlockPremium,
          updatePenaltySelections,
          updateIndividualPenaltySelections,
          retrySetDefaultPenalties,
          setNotificationPreference,
      }}
      >
      {children}
      </AppSettingsContext.Provider>
  );
};

export const useAppSettings = () => useContext(AppSettingsContext);